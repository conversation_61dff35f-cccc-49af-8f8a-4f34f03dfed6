package com.tapdata.tm.base.service;

import com.tapdata.manager.common.utils.JsonUtil;
import com.tapdata.tm.BaseJunit;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.ds.bean.NoSchemaFilter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

class BaseServiceTest extends BaseJunit {

    @Autowired
    BaseService baseService;

    @Test
    void find() {
    }

    @Test
    void testFind() {
    }

    @Test
    void findAll() {
    }

    @Test
    void testFindAll() {
    }

    @Test
    void testFindAll1() {
    }

    @Test
    void save() {
    }

    @Test
    void testSave() {
    }

    @Test
    void beforeSave() {
    }

    @Test
    void deleteById() {
    }

    @Test
    void findById() {
    }

    @Test
    void testFindById() {
    }

    @Test
    void testFindById1() {
    }

    @Test
    void testFindById2() {
    }

    @Test
    void findOne() {
    }

    @Test
    void testFindOne() {
    }

    @Test
    void testFindOne1() {
    }

    @Test
    void convertToDto() {
    }

    @Test
    void testConvertToDto() {
    }

    @Test
    void convertToEntity() {
    }

    @Test
    void testConvertToEntity() {
    }

    @Test
    void updateById() {
    }

    @Test
    void testUpdateById() {
    }

    @Test
    void updateByWhere() {
    }

    @Test
    void testUpdateByWhere() {
    }

    @Test
    void upsert() {
    }

    @Test
    void upsertByWhere() {
    }

    @Test
    void testFindAll2() {
    }

    @Test
    void update() {
    }

    @Test
    void testUpdate() {
    }

    @Test
    void findAndModify() {
    }

    @Test
    void deleteAll() {
    }

    @Test
    void testDeleteAll() {
    }

    @Test
    void count() {
    }

    @Test
    void testCount() {
    }

    @Test
    void testCount1() {
    }

    @Test
    void replaceById() {
    }

    @Test
    void replaceOrInsert() {
    }
}