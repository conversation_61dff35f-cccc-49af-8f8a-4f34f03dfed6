{"version": "1.2.0", "project": {"id": "9d7015d5936f4ca79a754428118a67fc", "name": "Employee Schema", "type": "SQL_SERVER", "lastModified": "2023-11-27T11:43:14.844Z", "schemasId": "c20799419a7d4feb9e35a5d5451dd5d1", "content": {"settings": {"viewMode": "VERTICAL", "shouldRecommendSchema": true, "casing": "CAMEL_CASE", "codegen": {"language": "JSON", "framework": "STANDARD_JSON_SCHEMA"}, "keyHandling": "GENERATED"}, "collections": {"9dda182a-16bc-42c7-8f13-afcc310e2806": {"name": "tEmp"}}, "mappings": {"65c032ce-cf6b-43fb-9520-02cc39c202f4": {"settings": {"type": "NEW_DOCUMENT", "notes": ""}, "fields": {"_id": {"target": {"name": "_id", "included": true, "type": "OBJECT_ID"}, "source": {"name": "_id", "databaseSpecificType": "NONE", "isPrimaryKey": false}}, "Login": {"target": {"name": "login", "included": true, "type": "STRING"}, "source": {"name": "<PERSON><PERSON>", "databaseSpecificType": "<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "EmpNum": {"target": {"name": "emp_num", "included": true, "type": "STRING"}, "source": {"name": "EmpNum", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "CardNum": {"target": {"name": "card_num", "included": true, "type": "STRING"}, "source": {"name": "CardNum", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Password": {"target": {"name": "password", "included": true, "type": "STRING"}, "source": {"name": "Password", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Title": {"target": {"name": "title", "included": true, "type": "STRING"}, "source": {"name": "Title", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "FirstName": {"target": {"name": "first_name", "included": true, "type": "STRING"}, "source": {"name": "FirstName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "LastName": {"target": {"name": "last_ame", "included": true, "type": "STRING"}, "source": {"name": "LastName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Addr1": {"target": {"name": "addr1", "included": true, "type": "STRING"}, "source": {"name": "Addr1", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Addr2": {"target": {"name": "addr2", "included": true, "type": "STRING"}, "source": {"name": "Addr2", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "City": {"target": {"name": "city", "included": false, "type": "STRING"}, "source": {"name": "City", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Tel1Type": {"target": {"name": "tel1Type", "included": false, "type": "STRING"}, "source": {"name": "Tel1Type", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Tel1": {"target": {"name": "tel1", "included": false, "type": "STRING"}, "source": {"name": "Tel1", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Tel2Type": {"target": {"name": "tel2Type", "included": false, "type": "STRING"}, "source": {"name": "Tel2Type", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Tel2": {"target": {"name": "tel2", "included": false, "type": "STRING"}, "source": {"name": "Tel2", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Email": {"target": {"name": "email", "included": false, "type": "STRING"}, "source": {"name": "Email", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "EmergName": {"target": {"name": "emerg<PERSON>ame", "included": false, "type": "STRING"}, "source": {"name": "Emerg<PERSON>ame", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "EmergTelType": {"target": {"name": "emergTelType", "included": false, "type": "STRING"}, "source": {"name": "EmergTelType", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "EmergTel": {"target": {"name": "emergTel", "included": false, "type": "STRING"}, "source": {"name": "<PERSON>ergTel", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Position": {"target": {"name": "position", "included": false, "type": "STRING"}, "source": {"name": "Position", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "EmpDefaultLocnID": {"target": {"name": "empDefaultLocnId", "included": false, "type": "STRING"}, "source": {"name": "EmpDefaultLocnID", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "IsInactive": {"target": {"name": "isInactive", "included": false, "type": "BOOL"}, "source": {"name": "IsInactive", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "LastPasswordComputerName": {"target": {"name": "lastPasswordComputerName", "included": false, "type": "STRING"}, "source": {"name": "LastPasswordComputerName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "PasswordCount": {"target": {"name": "passwordCount", "included": false, "type": "INTEGER"}, "source": {"name": "PasswordCount", "databaseSpecificType": "int", "isPrimaryKey": false}}, "PasswordAvgCharCount": {"target": {"name": "passwordAvgCharCount", "included": false, "type": "INTEGER"}, "source": {"name": "PasswordAvgCharCount", "databaseSpecificType": "int", "isPrimaryKey": false}}, "LTDRedeemPts": {"target": {"name": "ltdRedeemPts", "included": false, "type": "INTEGER"}, "source": {"name": "LTDRedeemPts", "databaseSpecificType": "int", "isPrimaryKey": false}}, "LTDAuthAward": {"target": {"name": "ltdAuthAward", "included": false, "type": "DECIMAL"}, "source": {"name": "LTDAuthAward", "databaseSpecificType": "money", "isPrimaryKey": false}}, "CompCode": {"target": {"name": "compCode", "included": false, "type": "STRING"}, "source": {"name": "CompCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "LastAuthAltCompDtAuthAward": {"target": {"name": "lastAuthAltCompDtAuthAward", "included": false, "type": "DECIMAL"}, "source": {"name": "LastAuthAltCompDtAuthAward", "databaseSpecificType": "money", "isPrimaryKey": false}}, "IsHost": {"target": {"name": "isHost", "included": false, "type": "BOOL"}, "source": {"name": "IsHost", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "IsSystemUse": {"target": {"name": "isSystemUse", "included": false, "type": "BOOL"}, "source": {"name": "IsSystemUse", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "PlDirReturnRows": {"target": {"name": "plDirReturnRows", "included": false, "type": "INTEGER"}, "source": {"name": "PlDirReturnRows", "databaseSpecificType": "int", "isPrimaryKey": false}}, "IsLocked": {"target": {"name": "isLocked", "included": false, "type": "BOOL"}, "source": {"name": "IsLocked", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "IsDefaultCasinoPassword": {"target": {"name": "isDefaultCasinoPassword", "included": false, "type": "BOOL"}, "source": {"name": "IsDefaultCasinoPassword", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "LoginAttempts": {"target": {"name": "loginAttempts", "included": false, "type": "INTEGER"}, "source": {"name": "LoginAttempts", "databaseSpecificType": "int", "isPrimaryKey": false}}, "MaxOverLimitTransactionPct": {"target": {"name": "maxOverLimitTransactionPct", "included": false, "type": "DECIMAL"}, "source": {"name": "MaxOverLimitTransactionPct", "databaseSpecificType": "decimal", "isPrimaryKey": false}}, "CreatedBy": {"target": {"name": "created<PERSON>y", "included": false, "type": "INTEGER"}, "source": {"name": "CreatedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "ModifiedBy": {"target": {"name": "modifiedBy", "included": false, "type": "INTEGER"}, "source": {"name": "ModifiedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "StateId": {"target": {"name": "stateId", "included": false, "type": "INTEGER"}, "source": {"name": "StateId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "StateName": {"target": {"name": "stateName", "included": false, "type": "STRING"}, "source": {"name": "StateName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "CountryId": {"target": {"name": "countryId", "included": false, "type": "INTEGER"}, "source": {"name": "CountryId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CountryName": {"target": {"name": "countryName", "included": false, "type": "STRING"}, "source": {"name": "CountryName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "PostalCodeId": {"target": {"name": "postalCodeId", "included": false, "type": "INTEGER"}, "source": {"name": "PostalCodeId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "PostalCode": {"target": {"name": "postalCode", "included": false, "type": "STRING"}, "source": {"name": "PostalCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "EmpTemplateId": {"target": {"name": "empTemplateId", "included": false, "type": "INTEGER"}, "source": {"name": "EmpTemplateId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "LanguageId": {"target": {"name": "languageId", "included": false, "type": "INTEGER"}, "source": {"name": "LanguageId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "AddressValidated": {"target": {"name": "addressValidated", "included": false, "type": "BOOL"}, "source": {"name": "AddressValidated", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "AddressOverriden": {"target": {"name": "addressOverriden", "included": false, "type": "BOOL"}, "source": {"name": "AddressOverriden", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "AddressOverrideReason": {"target": {"name": "addressOverrideReason", "included": false, "type": "STRING"}, "source": {"name": "AddressOverrideReason", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "BatchValidationStatus": {"target": {"name": "batchValidationStatus", "included": false, "type": "BOOL"}, "source": {"name": "BatchValidationStatus", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "BatchAddressValidated": {"target": {"name": "batchAddressValidated", "included": false, "type": "BOOL"}, "source": {"name": "BatchAddressValidated", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "BatchAddressCorrectionCode": {"target": {"name": "batchAddressCorrectionCode", "included": false, "type": "STRING"}, "source": {"name": "BatchAddressCorrectionCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "OldAddressId": {"target": {"name": "oldAddressId", "included": false, "type": "INTEGER"}, "source": {"name": "OldAddressId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CultureName": {"target": {"name": "cultureName", "included": false, "type": "STRING"}, "source": {"name": "CultureName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "AddressValidatedDtm": {"target": {"name": "addressValidatedDtm", "included": false, "type": "STRING"}, "source": {"name": "AddressValidatedDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "CreatedDtm": {"target": {"name": "createdDtm", "included": false, "type": "STRING"}, "source": {"name": "CreatedDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "ModifiedDtm": {"target": {"name": "modifiedDtm", "included": false, "type": "STRING"}, "source": {"name": "ModifiedDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "LastPasswordDtm": {"target": {"name": "lastPasswordDtm", "included": false, "type": "STRING"}, "source": {"name": "LastPasswordDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "PasswordConfigId": {"target": {"name": "passwordConfigId", "included": false, "type": "INTEGER"}, "source": {"name": "PasswordConfigId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "AccessLevelId": {"target": {"name": "accessLevelId", "included": false, "type": "INTEGER"}, "source": {"name": "AccessLevelId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "DataRowVersion": {"target": {"name": "dataRowVersion", "included": false, "type": "INTEGER"}, "source": {"name": "DataRowVersion", "databaseSpecificType": "int", "isPrimaryKey": false}}, "UserMatrixAccessType": {"target": {"name": "userMatrixAccessType", "included": false, "type": "BOOL"}, "source": {"name": "UserMatrixAccessType", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "DomainUserName": {"target": {"name": "domainUserName", "included": false, "type": "STRING"}, "source": {"name": "DomainUserName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "EmpLevel": {"target": {"name": "empLevel", "included": false, "type": "INTEGER"}, "source": {"name": "EmpLevel", "databaseSpecificType": "int", "isPrimaryKey": false}}, "MaxOverLimit": {"target": {"name": "maxOverLimit", "included": false, "type": "DECIMAL"}, "source": {"name": "MaxOverLimit", "databaseSpecificType": "money", "isPrimaryKey": false}}, "SMSEmployeeInfo": {"target": {"name": "smsEmployeeInfo", "included": false, "type": "STRING"}, "source": {"name": "SMSEmployeeInfo", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "UserMatrixRowVersion": {"target": {"name": "userMatrixRowVersion", "included": false, "type": "LONG"}, "source": {"name": "UserMatrixRowVersion", "databaseSpecificType": "bigint", "isPrimaryKey": false}}, "EmpId": {"target": {"name": "emp_id", "included": true, "type": "INTEGER"}, "source": {"name": "EmpId", "databaseSpecificType": "int", "isPrimaryKey": true}}, "RoleId": {"target": {"name": "roleId", "included": false, "type": "INTEGER"}, "source": {"name": "RoleId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "TeamId": {"target": {"name": "teamId", "included": false, "type": "LONG"}, "source": {"name": "TeamId", "databaseSpecificType": "bigint", "isPrimaryKey": false}}, "MaxParallelLogin": {"target": {"name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "included": false, "type": "INTEGER"}, "source": {"name": "MaxParallel<PERSON><PERSON>in", "databaseSpecificType": "int", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "9dda182a-16bc-42c7-8f13-afcc310e2806", "table": "TAPDATA.dbo.tEmp"}, "d91c31c4-93cb-4d40-ad1c-c4e8901d2e78": {"settings": {"type": "EMBEDDED_DOCUMENT", "notes": "", "embeddedPath": ""}, "fields": {"LanguageCode": {"target": {"name": "language_code", "included": true, "type": "STRING"}, "source": {"name": "LanguageCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": true}}, "LanguageName": {"target": {"name": "language_name", "included": true, "type": "STRING"}, "source": {"name": "LanguageName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "CharacterSet": {"target": {"name": "characterSet", "included": false, "type": "STRING"}, "source": {"name": "CharacterSet", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "CultureName": {"target": {"name": "cultureName", "included": false, "type": "STRING"}, "source": {"name": "CultureName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "IsInactive": {"target": {"name": "isInactive", "included": false, "type": "BOOL"}, "source": {"name": "IsInactive", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "CreatedBy": {"target": {"name": "created<PERSON>y", "included": false, "type": "INTEGER"}, "source": {"name": "CreatedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "ModifiedBy": {"target": {"name": "modifiedBy", "included": false, "type": "INTEGER"}, "source": {"name": "ModifiedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CreatedDtm": {"target": {"name": "createdDtm", "included": false, "type": "STRING"}, "source": {"name": "CreatedDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "ModifiedDtm": {"target": {"name": "modifiedDtm", "included": false, "type": "STRING"}, "source": {"name": "ModifiedDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "DataRowVersion": {"target": {"name": "dataRowVersion", "included": false, "type": "INTEGER"}, "source": {"name": "DataRowVersion", "databaseSpecificType": "int", "isPrimaryKey": false}}, "LanguageId": {"target": {"name": "languageId", "included": false, "type": "INTEGER"}, "source": {"name": "LanguageId", "databaseSpecificType": "int", "isPrimaryKey": true}}}, "calculatedFields": {}, "collectionId": "9dda182a-16bc-42c7-8f13-afcc310e2806", "table": "TAPDATA.dbo.tLanguage"}, "089f6d8a-4fc3-46f6-afb3-48a7520d7e77": {"settings": {"type": "EMBEDDED_DOCUMENT", "notes": "", "embeddedPath": ""}, "fields": {"StateId": {"target": {"name": "stateId", "included": false, "type": "INTEGER"}, "source": {"name": "StateId", "databaseSpecificType": "int", "isPrimaryKey": true}}, "StateCode": {"target": {"name": "state_code", "included": true, "type": "STRING"}, "source": {"name": "StateCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "StateName": {"target": {"name": "state_name", "included": true, "type": "STRING"}, "source": {"name": "StateName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "CountryId": {"target": {"name": "countryId", "included": false, "type": "INTEGER"}, "source": {"name": "CountryId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "IsInactive": {"target": {"name": "isInactive", "included": false, "type": "BOOL"}, "source": {"name": "IsInactive", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "CreatedBy": {"target": {"name": "created<PERSON>y", "included": false, "type": "INTEGER"}, "source": {"name": "CreatedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "ModifiedBy": {"target": {"name": "modifiedBy", "included": false, "type": "INTEGER"}, "source": {"name": "ModifiedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "W2GCopyCount": {"target": {"name": "w2GCopyCount", "included": false, "type": "INTEGER"}, "source": {"name": "W2GCopyCount", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CreatedDtm": {"target": {"name": "createdDtm", "included": false, "type": "STRING"}, "source": {"name": "CreatedDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "ModifiedDtm": {"target": {"name": "modifiedDtm", "included": false, "type": "STRING"}, "source": {"name": "ModifiedDtm", "databaseSpecificType": "datetimeoffset", "isPrimaryKey": false}}, "DataRowVersion": {"target": {"name": "dataRowVersion", "included": false, "type": "INTEGER"}, "source": {"name": "DataRowVersion", "databaseSpecificType": "int", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "9dda182a-16bc-42c7-8f13-afcc310e2806", "table": "TAPDATA.dbo.tState"}, "f9c19fad-9184-44b7-9fec-d40e466101ad": {"settings": {"type": "EMBEDDED_DOCUMENT", "notes": "", "embeddedPath": ""}, "fields": {"TeamId": {"target": {"name": "teamId", "included": false, "type": "INTEGER"}, "source": {"name": "TeamId", "databaseSpecificType": "int", "isPrimaryKey": true}}, "TeamCode": {"target": {"name": "team_code", "included": true, "type": "STRING"}, "source": {"name": "TeamCode", "databaseSpecificType": "<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "TeamDesc": {"target": {"name": "team_desc", "included": true, "type": "STRING"}, "source": {"name": "TeamDesc", "databaseSpecificType": "<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "isInactive": {"target": {"name": "isInactive", "included": false, "type": "BOOL"}, "source": {"name": "isInactive", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "isDefault": {"target": {"name": "isDefault", "included": false, "type": "BOOL"}, "source": {"name": "isDefault", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "CreatedBy": {"target": {"name": "created<PERSON>y", "included": false, "type": "INTEGER"}, "source": {"name": "CreatedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "ModifiedBy": {"target": {"name": "modifiedBy", "included": false, "type": "INTEGER"}, "source": {"name": "ModifiedBy", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CreatedOn": {"target": {"name": "createdOn", "included": false, "type": "DATE"}, "source": {"name": "CreatedOn", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "ModifiedOn": {"target": {"name": "modifiedOn", "included": false, "type": "DATE"}, "source": {"name": "ModifiedOn", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "DataRowVersion": {"target": {"name": "dataRowVersion", "included": false, "type": "INTEGER"}, "source": {"name": "DataRowVersion", "databaseSpecificType": "int", "isPrimaryKey": false}}, "TeamName": {"target": {"name": "teamName", "included": false, "type": "STRING"}, "source": {"name": "TeamName", "databaseSpecificType": "<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "isHost": {"target": {"name": "isHost", "included": false, "type": "BOOL"}, "source": {"name": "isHost", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "isTelemarketer": {"target": {"name": "isTelemarketer", "included": false, "type": "BOOL"}, "source": {"name": "isTelemarketer", "databaseSpecificType": "bit", "isPrimaryKey": false}}, "CasinoId": {"target": {"name": "team_casino_id", "included": true, "type": "INTEGER"}, "source": {"name": "CasinoId", "databaseSpecificType": "int", "isPrimaryKey": false}}, "TeamContactMail": {"target": {"name": "teamContactMail", "included": false, "type": "STRING"}, "source": {"name": "TeamContactMail", "databaseSpecificType": "<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "9dda182a-16bc-42c7-8f13-afcc310e2806", "table": "TAPDATA.dbo.tTeam"}}, "relationships": {"tables": {"TAPDATA.dbo.tEmp": {"mappings": ["65c032ce-cf6b-43fb-9520-02cc39c202f4"]}, "TAPDATA.dbo.tLanguage": {"mappings": ["d91c31c4-93cb-4d40-ad1c-c4e8901d2e78"]}, "TAPDATA.dbo.tState": {"mappings": ["089f6d8a-4fc3-46f6-afb3-48a7520d7e77"]}, "TAPDATA.dbo.tTeam": {"mappings": ["f9c19fad-9184-44b7-9fec-d40e466101ad"]}}, "collections": {"9dda182a-16bc-42c7-8f13-afcc310e2806": {"mappings": ["65c032ce-cf6b-43fb-9520-02cc39c202f4", "d91c31c4-93cb-4d40-ad1c-c4e8901d2e78", "089f6d8a-4fc3-46f6-afb3-48a7520d7e77", "f9c19fad-9184-44b7-9fec-d40e466101ad"]}}, "mappings": {"65c032ce-cf6b-43fb-9520-02cc39c202f4": {"children": ["d91c31c4-93cb-4d40-ad1c-c4e8901d2e78", "089f6d8a-4fc3-46f6-afb3-48a7520d7e77", "f9c19fad-9184-44b7-9fec-d40e466101ad"]}, "d91c31c4-93cb-4d40-ad1c-c4e8901d2e78": {"children": []}, "089f6d8a-4fc3-46f6-afb3-48a7520d7e77": {"children": []}, "f9c19fad-9184-44b7-9fec-d40e466101ad": {"children": []}}}, "diagrams": {"activeTab": "211f3e69-94f9-47c8-ab6c-2bf6bc850eec", "tabs": [{"id": "211f3e69-94f9-47c8-ab6c-2bf6bc850eec", "name": "Main Diagram", "relational": {"nodes": [{"id": "TAPDATA.dbo.tEmp", "type": "entityCard", "position": {"x": 783.8792085235921, "y": 9.858143074581335}, "width": 244, "height": 1304, "hidden": false}, {"id": "TAPDATA.dbo.tState", "type": "entityCard", "position": {"x": 129.94946727549473, "y": 882.744596651446}, "width": 244, "height": 242, "hidden": false}, {"id": "TAPDATA.dbo.tTeam", "type": "entityCard", "position": {"x": 113.30045662100406, "y": 502.87792998477937}, "width": 244, "height": 314, "hidden": false}, {"id": "TAPDATA.dbo.tLanguage", "type": "entityCard", "position": {"x": 130.29771689497693, "y": 149.0295281582953}, "width": 244, "height": 242, "hidden": false}], "edges": [{"id": "afdca3b2-131d-452c-8e12-887c5fe67794", "source": "TAPDATA.dbo.tEmp", "target": "TAPDATA.dbo.tEmp", "markerStart": "START_ONE", "markerEnd": "END_ONE"}, {"id": "dbd69f95-a522-4a90-8b8e-f0fddf903bb2", "source": "TAPDATA.dbo.tEmp", "target": "TAPDATA.dbo.tState", "markerStart": "START_ONE", "markerEnd": "END_ONE"}, {"id": "4bc4f2c9-9b6f-48c1-b365-e46f6f623d23", "source": "TAPDATA.dbo.tEmp", "target": "TAPDATA.dbo.tTeam", "markerStart": "START_ONE", "markerEnd": "END_ONE"}, {"id": "5f043c59-0a77-4d13-a98a-702e626d8050", "source": "TAPDATA.dbo.tEmp", "target": "TAPDATA.dbo.tLanguage", "markerStart": "START_ONE", "markerEnd": "END_ONE"}]}, "collection": {"nodes": [{"id": "tEmp", "type": "entityCard", "position": {"x": 100, "y": 100}, "width": 244, "height": 368, "hidden": false}], "edges": []}}]}}, "connectionDetails": {"jdbc": {"type": "SQL_SERVER", "url": "*************************************************************************************************", "user": "sa", "isManualUri": false, "host": "************", "port": "31433", "database": "TAPDATA", "mssqlAuthentication": "SQL_SERVER", "useSSL": true, "trustServerCertificate": true, "savePassword": true}}}, "schema": {"id": "c20799419a7d4feb9e35a5d5451dd5d1", "full": {"databases": {"TAPDATA": {"schemas": {"dbo": {"tables": {"aaa": {"type": "TABLE", "size": -1, "columns": {"s3": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s4": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s5": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s6": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s7": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s8": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "created": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s9": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "title": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s10": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 13, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "uniqueidentifier", "precision": 36, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__aaa__3213E83E7202A869"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "s1": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "s2": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__aaa__3213E83E7202A869": ["id"]}}, "tEmp": {"type": "TABLE", "size": -1, "columns": {"CreatedDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 55, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Addr1": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "AddressValidatedDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 54, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Addr2": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Email": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 255, "scale": 0}, "ordinalPosition": 15, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LoginAttempts": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 34, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "StateName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 39, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LastPasswordComputerName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 30, "scale": 0}, "ordinalPosition": 22, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmpLevel": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 63, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CardNum": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "AddressOverrideReason": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 2147483647, "scale": 0}, "ordinalPosition": 48, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ModifiedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 37, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmpDefaultLocnID": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 20, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "BatchAddressValidated": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 50, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "BatchAddressCorrectionCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 256, "scale": 0}, "ordinalPosition": 51, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LTDAuthAward": {"type": {"databaseSpecificType": "money", "precision": 19, "scale": 4}, "ordinalPosition": 26, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IsHost": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 29, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmpId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 67, "primaryKey": {"name": "PK__tEmp__AF2DBB9941A7BD58"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "Tel2Type": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 13, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CountryId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 40, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmergTel": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 18, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "MaxOverLimitTransactionPct": {"type": {"databaseSpecificType": "decimal", "precision": 18, "scale": 0}, "ordinalPosition": 35, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CreatedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 36, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Position": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 19, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Login": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 60, "scale": 0}, "ordinalPosition": 1, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "City": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 128, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Tel1Type": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Tel2": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 14, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LastPasswordDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 57, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Tel1": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CompCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 27, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "AccessLevelId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 59, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "UserMatrixRowVersion": {"type": {"databaseSpecificType": "bigint", "precision": 19, "scale": 0}, "ordinalPosition": 66, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LastName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 25, "scale": 0}, "ordinalPosition": 7, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmergName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 16, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LastAuthAltCompDtAuthAward": {"type": {"databaseSpecificType": "money", "precision": 19, "scale": 4}, "ordinalPosition": 28, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LTDRedeemPts": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 25, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "SMSEmployeeInfo": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 2147483647, "scale": 0}, "ordinalPosition": 65, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CountryName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 41, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "StateId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 38, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PostalCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 43, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "DataRowVersion": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 60, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "MaxOverLimit": {"type": {"databaseSpecificType": "money", "precision": 19, "scale": 4}, "ordinalPosition": 64, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmpNum": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "AddressValidated": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 46, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmergTelType": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 17, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "UserMatrixAccessType": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 61, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IsInactive": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 21, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IsSystemUse": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 30, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Password": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 2147483647, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "FirstName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 25, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "AddressOverriden": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 47, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Title": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PasswordCount": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 23, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IsLocked": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 32, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ModifiedDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 56, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "TeamId": {"type": {"databaseSpecificType": "bigint", "precision": 19, "scale": 0}, "ordinalPosition": 69, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "RoleId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 68, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmpTemplateId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 44, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CultureName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 16, "scale": 0}, "ordinalPosition": 53, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PasswordConfigId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 58, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PlDirReturnRows": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 31, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PostalCodeId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 42, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "MaxParallelLogin": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 70, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "OldAddressId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 52, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PasswordAvgCharCount": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 24, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IsDefaultCasinoPassword": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 33, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "BatchValidationStatus": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 49, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "DomainUserName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 62, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LanguageId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 45, "foreignKey": {"name": "SFK_tEmp_LanguageId", "schema": "dbo", "table": "tEmp", "column": "LanguageId", "cardinality": "ZERO_TO_ONE", "synthetic": true}, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__tEmp__AF2DBB9941A7BD58": ["EmpId"]}}, "_tapdata_heartbeat_table": {"type": "TABLE", "size": -1, "columns": {"id": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 64, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK___tapdata__3213E83E2ECC25BE"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "ts": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 33, "scale": 6}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK___tapdata__3213E83E2ECC25BE": ["id"]}}, "tLanguage": {"type": "TABLE", "size": -1, "columns": {"CreatedDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 8, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LanguageCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 3, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__tLanguag__F1809D08959EBCBF"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "CultureName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CreatedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 6, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IsInactive": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 5, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "DataRowVersion": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LanguageName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ModifiedDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 9, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CharacterSet": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ModifiedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 7, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LanguageId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 11, "primaryKey": {"name": "PK__tLanguag__F1809D08959EBCBF"}, "foreignKey": {"name": "SFK_tLanguage_LanguageId", "schema": "dbo", "table": "tEmp", "column": "LanguageId", "cardinality": "ZERO_TO_ONE", "synthetic": true}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK__tLanguag__F1809D08959EBCBF": ["LanguageId", "LanguageCode"]}}, "tState": {"type": "TABLE", "size": -1, "columns": {"CountryId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 4, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CreatedDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 9, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "W2GCopyCount": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 8, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CreatedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 6, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IsInactive": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 5, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "StateName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 3, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "StateId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__tState__C3BA3B3AFAD1CC8F"}, "foreignKey": {"name": "SFK_tState_StateId", "schema": "dbo", "table": "tEmp", "column": "StateId", "cardinality": "ZERO_TO_ONE", "synthetic": true}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "StateCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 3, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "DataRowVersion": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ModifiedDtm": {"type": {"databaseSpecificType": "datetimeoffset", "precision": 34, "scale": 7}, "ordinalPosition": 10, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ModifiedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 7, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__tState__C3BA3B3AFAD1CC8F": ["StateId"]}}, "test22": {"type": "TABLE", "size": -1, "columns": {"name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "uniqueidentifier", "precision": 36, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__test22__3213E83F0268D7D0"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "age": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__test22__3213E83F0268D7D0": ["id"]}}, "FDM_Customer_Profile_v0": {"type": "TABLE", "size": -1, "columns": {"CREATED_AT": {"type": {"databaseSpecificType": "datetime2", "precision": 23, "scale": 3}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LASTNAME": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CUSTOMER_ID": {"type": {"databaseSpecificType": "float", "precision": 53, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "DEPARTMENT": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "FIRSTNAME": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "_id": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__FDM_Cust__DED88B1D87AFCF03"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "EMAIL": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 130, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__FDM_Cust__DED88B1D87AFCF03": ["_id"]}}, "aa01": {"type": "TABLE", "size": -1, "columns": {"a1": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__aa01__3213A9FAA6980AAA"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "a2": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__aa01__3213A9FAA6980AAA": ["a1"]}}, "sql_server_lhb_test_oracle": {"type": "TABLE", "size": -1, "columns": {"COLUMN_17": {"type": {"databaseSpecificType": "bigint", "precision": 19, "scale": 0}, "ordinalPosition": 17, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "UNIT": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "INPUT": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "OUTPUT": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CREATEDATE": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 40, "scale": 0}, "ordinalPosition": 14, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "AMT": {"type": {"databaseSpecificType": "real", "precision": 24, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CREATEEMP": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 13, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "GROUPID": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 30, "scale": 0}, "ordinalPosition": 2, "primaryKey": {"name": "PK__sql_serv__9CB2508369959449"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "test2": {"type": {"databaseSpecificType": "bigint", "precision": 19, "scale": 0}, "ordinalPosition": 15, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "test3": {"type": {"databaseSpecificType": "datetime2", "precision": 27, "scale": 7}, "ordinalPosition": 16, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "SRLNO": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 4, "primaryKey": {"name": "PK__sql_serv__9CB2508369959449"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "DATADATE": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 8, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__sql_serv__9CB2508369959449"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "QTY": {"type": {"databaseSpecificType": "real", "precision": 24, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "WCE": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "DPD": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "MEMO": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PGRMID": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 3, "primaryKey": {"name": "PK__sql_serv__9CB2508369959449"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK__sql_serv__9CB2508369959449": ["DATADATE", "GROUPID", "PGRMID", "SRLNO"]}}, "testddl": {"type": "TABLE", "size": -1, "columns": {"column_3": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "column_2": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "column_1": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "testddl_pk"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"testddl_pk": ["column_1"]}}, "Iphone_Order": {"type": "TABLE", "size": -1, "columns": {"ORDERID": {"type": {"databaseSpecificType": "decimal", "precision": 20, "scale": 8}, "ordinalPosition": 1, "primaryKey": {"name": "PK__Iphone_O__491E41935E9286B7"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "CUSTOMERID": {"type": {"databaseSpecificType": "decimal", "precision": 20, "scale": 8}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PRICE": {"type": {"databaseSpecificType": "decimal", "precision": 20, "scale": 8}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Employee_Bonus": {"type": {"databaseSpecificType": "real", "precision": 24, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PRODUCTNAME": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CREATETIME": {"type": {"databaseSpecificType": "datetime2", "precision": 26, "scale": 6}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__Iphone_O__491E41935E9286B7": ["ORDERID"]}}, "test_table_001": {"type": "TABLE", "size": -1, "columns": {"address": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 200, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "phone": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__test_tab__3213E83F179F6A38"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK__test_tab__3213E83F179F6A38": ["id"]}}, "testdatetime": {"type": "TABLE", "size": -1, "columns": {"a1": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__testdate__3213A9FB73303316"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "a2": {"type": {"databaseSpecificType": "datetime2", "precision": 19, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__testdate__3213A9FB73303316": ["a1"]}}, "FDM_CAR_CLAIM_v0": {"type": "TABLE", "size": -1, "columns": {"SETTLED_DATE": {"type": {"databaseSpecificType": "datetime2", "precision": 23, "scale": 3}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CLAIM_ID": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "SETTLED_AMOUNT": {"type": {"databaseSpecificType": "float", "precision": 53, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CLAIM_REASON": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "_id": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__FDM_CAR___DED88B1DA229B335"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "POLICY_ID": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 100, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CLAIM_DATE": {"type": {"databaseSpecificType": "datetime2", "precision": 23, "scale": 3}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LAST_CHANGE": {"type": {"databaseSpecificType": "datetime2", "precision": 23, "scale": 3}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CLAIM_AMOUNT": {"type": {"databaseSpecificType": "float", "precision": 53, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__FDM_CAR___DED88B1DA229B335": ["_id"]}}, "pa_data": {"type": "TABLE", "size": -1, "columns": {"QueDate": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PAADMType": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 1, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "QueDepDr": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PAPMINo": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "AS_OF_DATE": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "sfz": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "IcdUsed": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PAADMRowID": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 11, "primaryKey": {"name": "PK_test"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "PAPERAgeYr": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PAPERRowId": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 225, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_test": ["id"]}}, "testid_1": {"type": "TABLE", "size": -1, "columns": {"name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "uniqueidentifier", "precision": 36, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__testid_1__3213E83FC036F701"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "age": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__testid_1__3213E83FC036F701": ["id"]}}, "table_date": {"type": "TABLE", "size": -1, "columns": {"date": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "column_1": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "table_date_pk"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"table_date_pk": ["column_1"]}}, "testid1": {"type": "TABLE", "size": -1, "columns": {"name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "uniqueidentifier", "precision": 36, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__testid1__3213E83E0EE90C2B"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "column_5": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "column_4": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "age": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__testid1__3213E83E0EE90C2B": ["id"]}}, "product_test": {"type": "TABLE", "size": -1, "columns": {"flag": {"type": {"databaseSpecificType": "char", "precision": 4, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "productId": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 255, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 255, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "flag2": {"type": {"databaseSpecificType": "char", "precision": 4, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__product___3213E83F389E60A4"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "text": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 255, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__product___3213E83F389E60A4": ["id"]}}, "testid": {"type": "TABLE", "size": -1, "columns": {"name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "uniqueidentifier", "precision": 36, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__testid__3213E83FC1D714DF"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "column_5": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "column_4": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "age": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__testid__3213E83FC1D714DF": ["id"]}}, "tTeam": {"type": "TABLE", "size": -1, "columns": {"CreatedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ModifiedOn": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "TeamContactMail": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 200, "scale": 0}, "ordinalPosition": 15, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "isInactive": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "DataRowVersion": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "TeamName": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 50, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "TeamId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK__tTeam__123AE7994336E2EB"}, "foreignKey": {"name": "SFK_tTeam_TeamId", "schema": "dbo", "table": "tEmp", "column": "TeamId", "cardinality": "ZERO_TO_ONE", "synthetic": true}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "ModifiedBy": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "TeamDesc": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 150, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "isHost": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "isTelemarketer": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 13, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "isDefault": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CasinoId": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 14, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "TeamCode": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CreatedOn": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK__tTeam__123AE7994336E2EB": ["TeamId"]}}, "test_1107": {"type": "TABLE", "size": -1, "columns": {"name": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "testId": {"type": {"databaseSpecificType": "char", "precision": 1, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "id": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "test_1107_pk"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "text": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "content": {"type": {"databaseSpecificType": "<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"test_1107_pk": ["id"]}}}}}}}, "metadata": {"databaseType": "SQL_SERVER", "databaseVersion": "14.00.3370"}}, "imported": {"TAPDATA": {"dbo": {"tEmp": {}, "tState": {}, "tTeam": {}, "tLanguage": {}}}}}}