package com.tapdata.tm.init;

import lombok.NonNull;

import java.util.List;
import java.util.function.Predicate;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2022/12/16 15:48 Create
 */
public interface IPatchScanner {

    /**
     * scan patch by function
     *
     * @param patches   patch list
     * @param isVersion check function
     */
    void scanPatches(@NonNull List<IPatch> patches, @NonNull Predicate<PatchVersion> isVersion);
}
