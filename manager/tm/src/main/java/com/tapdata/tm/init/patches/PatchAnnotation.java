package com.tapdata.tm.init.patches;

import com.tapdata.tm.init.PatchTypeEnums;
import io.tapdata.utils.AppType;

import java.lang.annotation.*;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2022/12/16 16:59 Create
 */
@Inherited
@Documented
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(PatchAnnotations.class)
public @interface PatchAnnotation {
    String version();

    AppType appType() default AppType.DAAS;

    PatchTypeEnums patchType() default PatchTypeEnums.Script;
}
