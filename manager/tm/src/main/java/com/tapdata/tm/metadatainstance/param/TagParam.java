//package com.tapdata.tm.metadatainstance.param;
//
//import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import com.tapdata.tm.commons.base.convert.ObjectIdDeserialize;
//import com.tapdata.tm.commons.base.convert.ObjectIdSerialize;
//import lombok.Data;
//import org.bson.types.ObjectId;
//import org.springframework.data.mongodb.core.index.Indexed;
//
///**
// * @Author: Zed
// * @Date: 2021/9/2
// * @Description: connect内嵌tag文档
// */
//@Data
//public class TagParam {
//
//    /**
//     * 分类id
//     */
//    private String id;
//    /**
//     * 分类名称
//     */
//    private String value;
//
//}
