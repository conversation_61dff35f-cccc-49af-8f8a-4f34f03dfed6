//package com.tapdata.tm.scheduleTasks.param;
//
//import com.mongodb.BasicDBObject;
//import lombok.Data;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.Map;
//
//@Data
//public class SaveSchduleTaskParam {
//    private String task_type;
//    private String type;
//    private Long period;
//    private String status;
//    private String task_name;
//    private String task_profile;
//    private String agent_id;
//    private Date last_updated;
//    private Long ping_time;
//    private ArrayList<BasicDBObject> thread;
//    private Map<String, Object> task_data;
//    private String filter;
//    private Object statsOffset;
//}
//
