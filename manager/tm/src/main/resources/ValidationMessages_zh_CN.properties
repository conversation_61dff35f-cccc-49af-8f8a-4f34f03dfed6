SystemError=\u7CFB\u7D71\u5904\u7406\u5931\u8D25: {0}
IllegalState=\u65E0\u6548\u72B6\u6001: {0}
IllegalArgument=\u65E0\u6548\u53C2\u6570: {0}
javax.validation.constraints.ValueOfEnum.message=\u53EF\u9009\u503C {enums}
javax.validation.constraints.CustomerTypeSubset.message=\u53EF\u9009\u503C {anyOf}
javax.validation.constraints.In.message=\u5FC5\u987B\u4E3A {values} \u4E2D\u7684\u4EFB\u610F\u4E00\u4E2A\u503C
NotLogin=\u7528\u6237\u6CA1\u6709\u767B\u5F55
CreateAgentFailed.AccessFailed=\u65E0\u6CD5\u8BBF\u95EETM\u670D\u52A1\u5668\u3002
SubscribeFailed.OrderLimit=\u540C\u4E00\u4E2A\u8D44\u6E90\u6C60\u6700\u591A\u53EA\u80FD\u8BA2\u8D2D{0}\u4E2A\u5B9E\u4F8B
QueryUserInfoFromAuthingFailed=\u67E5\u8BE2Authing\u7528\u6237\u4FE1\u606F\u5931\u8D25
Resources.NotEnough=\u8D44\u6E90\u4E0D\u8DB3




#Inspect
inspect.name.exist=\u540D\u79F0\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539
Inspect.agentTag.null=tag\u4E0D\u80FD\u4E3A\u7A7A
Inspect.task.null=task\u4E0D\u80FD\u4E3A\u7A7A
#function
function.name.exist=\u540D\u79F0\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539

#MetadataDefinition
MetadataDefinition.Value.Exist=\u5206\u7C7B\u540D\u79F0\u5DF2\u5B58\u5728


#reset password
Email.Not.Exist=\u90AE\u7BB1\u4E0D\u5B58\u5728
Email.Send.fail=\u90AE\u4EF6\u53D1\u9001\u5931\u8D25
Email.verify.code.Send.fail=\u90AE\u4EF6\u53D1\u9001\u5931\u8D25

#modules
Modules.Name.Existed=api\u540D\u79F0\u5DF2\u5B58\u5728
Modules.BasePathAndVersion.Existed=api \u57FA\u7840\u8DEF\u5F84\u5DF2\u5B58\u5728
Modules.Connection.Null= \u6570\u636E\u5E93\u8FDE\u63A5\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u4FEE\u6539
Datasource.LinkModules=\u5B58\u5728\u5173\u8054\u7684api\uFF0C\u4E0D\u80FD\u5220\u9664

User.Not.Exist= \u7528\u6237\u4E0D\u5B58\u5728
User.email.Found=\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u90AE\u7BB1
ValidateCode.Not.Incorrect=\u9A8C\u8BC1\u7801\u9519\u8BEF
ValidateCode.Is.expired=\u9A8C\u8BC1\u7801\u5DF2\u8FC7\u671F


