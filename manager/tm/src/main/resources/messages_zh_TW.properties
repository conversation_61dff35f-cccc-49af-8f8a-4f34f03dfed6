user.name=无效状态

NotLogin=用户没有登录
NotAuthorized=用戶沒有權限
Clear.Slot={0}
InvalidPaidPlan=無效的付費方案，請檢查並訂閱付費方案
Task.CronError = 調度運算式錯誤
Role.Unable.Delete=無法刪除,當前角色存在綁定用戶

Inspect.Recovery.NotFieldMethod=非字段校驗任務
Inspect.Recovery.StatusNotDone=校驗任務不是已完成狀態
Inspect.Recovery.ResultNotFound=校驗任務沒有差異結果
Inspect.Recovery.IsNotWithTask=校驗不是基於同步任務創建
Inspect.Recovery.TaskNotFound=同步任務不存在
Inspect.Recovery.TaskTypeIsNotCDC=同步任務不包含增量
Inspect.Recovery.TaskIsNotRunning=同步任務非運行狀態
Inspect.Recovery.TaskSyncStatusIsNotCDC=同步任務不在增量階段
Inspect.ExportEventSql.TargetConnectionNotSupport=目標數據源暫不支持導出修復SQL
Inspect.ExportEventSql.TargetConnectionNotFound=目標連接不存在
Inspect.ExportEventSql.fail=導出修復SQL失敗
Inspect.ExportEventSql.Exporting=修復文件正在導出中
Inspect.ExportEventSql.Exported=修復文件已導出

Incorrect.Vague=郵箱或密碼錯誤，請檢查後重新輸入
# external storage
External.Storage.Name.Blank=名稱不能為空
External.Storage.Name.Exists=名稱 {0} 已經存在
External.Storage.Type.Blank=存儲類型不能為空
External.Storage.Type.Invalid=非法存儲類型: {0}
External.Storage.MongoDB.Uri.Blank=MongoDB連接不能為空
\External.Storage.MongoDB.Database.Blank=MongoDB連接的庫名不能為空
External.Storage.MongoDB.Table.Blank=MongoDB表名不能為空
External.Storage.RocksDB.Path.Blank=RocksDb存儲路徑不能為空
External.Storage.Cannot.Delete=無法刪除,該配置正在被使用\n任務數量: {0}\n任務名稱: {1}
External.Storage.ID.NULL=外存id不能為空
External.Storage.MongoDB.Old.Pwd.NULL=替換脫敏密碼失敗，在舊的MongoDB連接信息中提取的密碼為空: {0}

v2_dashboard=工作台
v2_data-console=數據面板
v2_datasource_menu=連接管理
v2_data_pipeline=數據管道
v2_advanced_features=高級功能
v2_data_replication=數據複製
v2_data_flow=數據開發
v2_data_check=數據校驗
v2_log_collector=共享挖掘
v2_function_management=函數管理
v2_custom_node=自定義節點
v2_shared_cache=共享緩存
v2_data_discovery=數據轉換
v2_data_object=數據對象
v2_data_catalogue=數據目錄
v2_data-server=數據服務
v2_api-application=應用管理
v2_data-server-list=服務管理
v2_api-client=客戶端
v2_api-servers=服務器
v2_data_server_audit=服務審計
v2_api_monitor=服務監控
v2_system-management=系統管理
v2_external-storage_menu=外存管理
v2_cluster-management_menu=集群管理
v2_user_management_menu=用戶管理
v2_role_management=角色管理

JsFunction.ImportFormatError=函數導入文件格式錯誤
JsFunction.ImportFormatError.function.name.empty=函數名不能為空
JsFunction.ImportFormatError.function.body.empty=函數體不能為空

share_cdc_task_stop_warning=該任務所使用的共享挖掘任務已停止，請盡快恢復
task_status_error=錯誤
task_status_stop=停止

#Type Message
ARRAY=數組
BINARY=字節數組
BOOLEAN=布爾值
DATE=日期
DATETIME=日期時間
MAP=映射
NUMBER=數值
STRING=字符串
TIME=時間
YEAR=日期（年）
UNKNOW=未知

#Log Message
AllNotices=全部通知
DeleteNotifications=刪除通知
NotificationSettings=通知設置
ReadNotifications=已讀通知
EnterpriseInformation=企業信息

DAG.JonNode=Join節點需要配寘兩個前置節點

#TapOssNonSupportFunctionException
TapOssNonSupportFunctionException=開源版本不支持該功能

#AgentGroup
group.repeat=名稱已存在
group.agent.not.fund=當前Agent不存在：{0}
group.agent.repeatedly=引擎不能重複添加到相同標籤
group.agent.add.failed=引擎添加標籤失敗，請重試
group.agent.remove.failed=引擎移除標籤失敗，請重試
group.response.body.failed=請求參數不能為空
group.id.empty=標籤ID不能為空
group.agent.id.empty=引擎ID不能為空
group.new.name.empty=新名稱不能為空
group.new.name.too.long=新名稱字元長度不能大於{0}
group.not.fund=標籤不存在：{0}
group.agent.not.available=當前標籤暫無可用引擎關聯: {0}
group.agent.not.invalid=標籤ID無效
lack.group.agent=請補充Agent標籤

#task import
task.import.connection.check.ConnectorNotRegister=任務導入失敗，導入前請註冊資料來源：{0}，依賴的版本：{1}，如已注册請重試
task.import.connection.check.ConnectorVersionTooHeight=任務導入失敗，導入任務中使用的數據源 {0} 版本過高，當前環境使用的版本：{1}，導入任務中的版本：{2}，請陞級數據源 {3} 後重新導入

relMig.parse.failed=relmig檔案解析失敗，錯誤原因：{0}
relMig.parse.unSupport=當前relmig檔案版本{0}暫不支持，現時僅支持支持：{1}

#web hook
webhook.create.hook.defaultName=<默認Service Hook名稱>
webhook.url.too.long=請求URL允許填寫的最大長度是：{0}，實際填寫的長度：{1}
webhook.info.mark.too.long=備註文字允許填寫的最大長度是：{0}，實際填寫的長度：{1}
webhook.info.custom.http.headers.too.long=自定義請求頭文字允許填寫的最大長度是：{0}，實際填寫的長度：{1}
webhook.info.custom.template.too.long=自定義模版文字允許填寫的最大長度是：{0}，實際填寫的長度：{1}
webhook.info.custom.template.invalid=自定義請求體範本無效
webhook.info.custom.http.headers.invalid=自定義Http請求頭資訊無效，{0}
webhook.ping.failed=PING測試失敗{0}，請檢查您的WebHook URL
webhook.url.host.invalid=非法的IP和功能變數名稱配寘，不能使用當前系統的IP埠：{0}
webhook.ping.succeed=已發送測試PING事件到服務端，請查看發送記錄確認結果
webhook.url.invalid=參數校驗失敗，參數内容service_url格式不正確。 實際值： {0}，期望類型：合法的URI
webhook.info.existsById=WebHook配寘資訊不存在，WebHook ID: {0}
webhook.history.reSend=在重新發送歷史 WebHook 事件消息之前，此歷史事件ID不得為空
webhook.reOpen.failed=WebHook重新打開失敗，請重試
webhook.history.hook.id.error=WebHook ID 不能為空
webhook.history.page.from.error=分頁參數[pageFrom]無效，起始值應該大於等於0，實際值為：{0}
webhook.history.page.size.error=分頁參數[pageSize]無效，每頁大小應該大於0，實際值為：{0}

#auth
insufficient.permissions=許可權不足: {0}，当前操作需要权限：{1}
inspect.result.not.exists=當前校驗任務的校驗記錄已經不存在：{0}
tag.operate.not.allowed=當前用戶沒有權限操作該標簽

source.setting.check.cdc= 【增量檢查】數據源:{0}的配置增量功能不支持，不能配置增量任務

Modules.Permission.Scope.Null=權限範圍不能爲空
Modules.Tags.Null=所屬應用不能為空

export.inspectDetails.failed = 下載校驗詳情失敗 :{0}

AD.Login.Fail=通過LDAP登錄失敗，請聯系管理員
AD.Search.Fail=查詢LDAP用戶失敗，請聯系管理員
AD.Account.Not.Exists=LDAP用戶在當前組不存在，請聯系管理員
AD.Login.WrongPassword=通過LDAP登錄失敗，密碼或憑據無效，請檢查您的帳戶是否可用且密碼是否正確
AD.Login.InvalidCert=證書認證失敗，請確保證書有效
AD.Login.Retryable=網絡發生問題，請重試或聯系管理員

read.version.file.failed=系統版本無效

openapi.generator.version.exists=版本已存在
openapi.generator.package.name.empty=包名不能為空
openapi.generator.package.name.invalid.format=包名格式無效。必須遵循Java包命名規範：小寫字母、數字、下劃線，用點分隔，每個段必須以字母開頭
openapi.generator.package.name.contains.reserved.keyword=包名包含Java保留關鍵字：{0}
openapi.generator.reentry.blocked=SDK名稱為：{0} 的另一個版本正在進行生成。請在開始新的生成之前等待當前的生成完成
openapi.generator.unknown.error=代碼生成啟動失敗: {0}
openapi.generator.module.empty=請選擇API並重新生成SDK

module.save.check.not-empty=輸出結果中的欄位清單不能為空
module.save.check.repat=輸出結果中的欄位別名不能重複設定：{0}