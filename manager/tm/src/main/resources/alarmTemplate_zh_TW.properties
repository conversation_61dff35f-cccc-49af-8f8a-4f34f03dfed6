TASK_FULL_COMPLETE = 任務#{[taskName]}全量同步已完成，耗時#{[costTime]}ms, 全量完成時間點：#{[snapDoneDate]},告警時間：#{[alarmDate]}
TASK_INCREMENT_START = 任務#{[taskName]}增量同步已開始, 增量時間點#{[cdcTime]}, 告警時間：#{[alarmDate]}
TASK_STATUS_STOP_ERROR = 任務#{[taskName]}已出錯停止，請盡快處理！告警時間：#{[alarmDate]}
TASK_STATUS_STOP_MANUAL = 任務#{[taskName]}已被用戶[#{[updatorName]}]停止，請關註！告警時間：#{[alarmDate]}
TASK_INCREMENT_DELAY_START = 任務#{[taskName]}增量延遲#{[flag]}閾值#{[threshold]}ms，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
TASK_INCREMENT_DELAY_ALWAYS = 任務#{[taskName]}增量延遲#{[flag]}閾值#{[threshold]}ms，已持續#{[continueTime]}分鐘，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
TASK_INCREMENT_DELAY_RECOVER = 任務#{[taskName]}增量延遲已恢復正常，當前值：#{[currentValue]}ms。告警時間：#{[alarmDate]}
TASK_INSPECT_DIFFERENCE = 任務「#{[taskName]}」發現差異數據，#{[diffFromTs]} 到 #{[diffToTs]} 差異從 #{[diffFromTotals]} 變為 #{[diffToTotals]}
TASK_INSPECT_DIFFERENCE_RECOVER = 任務「#{[taskName]}」從 #{[diffFirstTs]} 到 #{[diffToTs]} 的差異已恢復
DATANODE_SOURCE_CANNOT_CONNECT = 任務#{[taskName]} 使用的源連接 [#{[sourceName]}]當前無法正常連接，請盡快處理！告警時間：#{[alarmDate]}
DATANODE_SOURCE_CANNOT_CONNECT_ALWAYS = 任務#{[taskName]} 使用的源連接 [#{[sourceName]}]當前無法正常連接，已持續#{[continueTime]}分鐘，請盡快處理！告警時間：#{[alarmDate]}
DATANODE_SOURCE_CANNOT_CONNECT_RECOVER = 任務#{[taskName]} 使用的源連接 [#{[sourceName]}]已恢復正常連接。告警時間：#{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_START = 任務#{[taskName]}的源節點[#{[nodeName]}]平均處理耗時#{[flag]}閾值#{[threshold]}ms，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_ALWAYS = 任務#{[taskName]}的源節點[#{[nodeName]}]平均處理耗時#{[flag]}閾值#{[threshold]}ms，已持續#{[continueTime]}分鐘，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_RECOVER = 任務#{[taskName]}的源節點[#{[nodeName]}]平均處理耗時已恢復正常，當前值：#{[currentValue]}ms。告警時間：#{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_START = 任務#{[taskName]}的處理節點[#{[nodeName]}]平均處理耗時#{[flag]}閾值#{[threshold]}ms，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_ALWAYS = 任務#{[taskName]}的處理節點[#{[nodeName]}]平均處理耗時#{[flag]}閾值#{[threshold]}ms，已持續#{[continueTime]}分鐘，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_RECOVER = 任務#{[taskName]}的處理節點[#{[nodeName]}]平均處理耗時已恢復正常，當前值：#{[currentValue]}ms。告警時間：#{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_START = 任務#{[taskName]}的目標節點[#{[nodeName]}]平均處理耗時#{[flag]}閾值#{[threshold]}ms，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_ALWAYS = 任務#{[taskName]}的目標節點[#{[nodeName]}]平均處理耗時#{[flag]}閾值#{[threshold]}ms，已持續#{[continueTime]}分鐘，當前值：#{[currentValue]}ms，請關註！告警時間：#{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_RECOVER = 任務#{[taskName]}的目標節點[#{[nodeName]}]平均處理耗時已恢復正常，當前值：#{[currentValue]}ms。告警時間：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_CHANGE_AGENT = 任務#{[taskName]}所在Agent[#{[agentName]}]已停止運行，當前還有#{[number]}個可用Agent，任務將重新調度到Agent[#{[otherAgentName]}]上運行，請關註！告警時間：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_NO_AGENT = 任務#{[taskName]}所在Agent[#{[agentName]}]已停止運行，當前已無可用Agent，將會影響任務正常運行，請盡快處理！告警時間：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_RECOVER = 任務#{[taskName]}所在Agent[#{[agentName]}]已恢復運行，告警時間：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_CLOUD = 任務#{[taskName]}所在Agent[#{[agentName]}]已停止運行，將會影響任務正常運行，請盡快處理！告警時間：#{[alarmDate]}
GREATER=大於
LESS=小於
INSPECT_TASK_ERROR = 您的校驗任務[#{[inspectName]}]已出錯停止，時間：#{[alarmDate]}，請關注。
INSPECT_COUNT_ERROR = 您的校驗任務[#{[inspectName]}]快速count校驗結果不一致，當前差異行數為:#{[count]}，请关注。告警時間：#{[alarmDate]}
INSPECT_VALUE_ALL_ERROR = 您的校驗任務[#{[inspectName]}]表全字段值校驗結果不一致，當前表數據差:#{[count]}，請關注。告警時間：#{[alarmDate]}
INSPECT_VALUE_JOIN_ERROR = 您的校驗任務[#{[inspectName]}]關聯字段值校驗結果不一致，當前表數據差:#{[count]}，請關注。告警時間：#{[alarmDate]}
TASK_STATUS_STOP = 任務#{[taskName]}已停止，停止時間點：#{[stopTime]}, 告警時間：#{[alarmDate]}
