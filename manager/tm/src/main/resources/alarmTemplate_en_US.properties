TASK_FULL_COMPLETE = Task #{[taskName]} full sync has completed, time #{[costTime]}ms, full completion time point: #{[snapDoneDate]}, alarm time: #{[alarmDate]}
TASK_INCREMENT_START = Task #{[taskName]} incremental synchronization has started, incremental time point #{[cdcTime]}, alarm time: #{[alarmDate]}
TASK_STATUS_STOP_ERROR = Task #{[taskName]} has been stopped with an error, please process as soon as possible! Alarm time: #{[alarmDate]}
TASK_STATUS_STOP_MANUAL = Task #{[taskName]} has been stopped by user [#{[updatorName]}], please attend to it! Alert time: #{[alarmDate]}
TASK_INCREMENT_DELAY_START = Task #{[taskName]} incremental delay #{[flag]} threshold #{[threshold]}ms, current value: #{[currentValue]}ms, please pay attention! Alarm time: #{[alarmDate]}
TASK_INCREMENT_DELAY_ALWAYS = Task #{[taskName]} incremental delay #{[flag]} threshold #{[threshold]}ms, has lasted #{[continueTime]}minutes, current value: #{[currentValue]}ms, please pay attention! Alarm time: #{[alarmDate]}
TASK_INCREMENT_DELAY_RECOVER = Task #{[taskName]} incremental delay is back to normal, current value: #{[currentValue]}ms. alarmTime: #{[alarmDate]}
TASK_INSPECT_DIFFERENCE = Task '#{[taskName]}' discovers the data differences, between #{[diffFromTs]} and #{[diffToTs]} changes from #{[diffFromTotals]} to #{[diffToTotals]}
TASK_INSPECT_DIFFERENCE_RECOVER = Recovery the data differences of Task '#{[taskName]}' within the time period from #{[diffFirstTs]} and #{[diffToTs]}
DATANODE_SOURCE_CANNOT_CONNECT = Task #{[taskName]} used source connection [#{[sourceName]}] is currently not connecting properly, please take care of it as soon as possible! Alarm time: #{[alarmDate]}
DATANODE_SOURCE_CANNOT_CONNECT_ALWAYS = The source connection [#{[sourceName]}] used by task #{[taskName]} is currently not connecting properly and has lasted #{[continueTime]} minutes, please handle it as soon as possible! Alarm time: #{[alarmDate]}
DATANODE_SOURCE_CANNOT_CONNECT_RECOVER = The source connection [#{[sourceName]}] used by task #{[taskName]} has been restored to a normal connection. Alarm time: #{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_START = Average processing time #{[flag]} threshold #{[threshold]}ms for source node [#{[nodeName]}] of task #{[taskName]}, current value: #{[currentValue]}ms , please pay attention! Alarm time: #{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_ALWAYS = The average processing time #{[flag]} threshold #{[threshold]}ms for the source node [#{[nodeName]}] of task #{[taskName]}, has lasted #{[continueTime]} minutes , current value: #{[currentValue]}ms, stay tuned! Alarm time: #{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_RECOVER = The average processing elapsed time of the source node [#{[nodeName]}] of task #{[taskName]} has returned to normal, current value: #{[currentValue]}ms. alarmDate: #{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_START = The average processing elapsed time #{[flag]} threshold #{[threshold]}ms for the processing node [#{[nodeName]}] of task#{[taskName]}, current value: #{[currentValue ]}ms, stay tuned! Alarm time: #{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_ALWAYS = Average processing elapsed time #{[flag]} threshold #{[threshold]}ms for task #{[taskName]}'s processing node [#{[nodeName]}], has lasted #{[continueTime ]} minutes, current value: #{[currentValue]}ms, stay tuned! Alarm time: #{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_RECOVER = The average processing elapsed time of the processing node [#{[nodeName]}] of task #{[taskName]} has returned to normal, current value: #{[currentValue]}ms. alarmDate: #{[ alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_START = Average processing elapsed time #{[flag]} threshold #{[threshold]}ms for target node [#{[nodeName]}] of task #{[taskName]}, current value: #{[currentValue]}ms. Stay tuned! Alarm time: #{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_ALWAYS = The target node [#{[nodeName]}] of task #{[taskName]} has taken an average processing time of #{[flag]} threshold #{[threshold]}ms, has lasted #{[continueTime]} minutes, and Current value: #{[currentValue]}ms, stay tuned! Alarm time: #{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_RECOVER = The average processing time of the target node [#{[nodeName]}] of task #{[taskName]} has returned to normal, current value: #{[currentValue]}ms. alarmDate: #{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_CHANGE_AGENT = The Agent[#{[agentName]}] where task #{[taskName]} is located has stopped running, there are #{[number]} available Agents, the task will be rescheduled to Agent[#{[ otherAgentName]}]] to run, please pay attention! Alarm time: #{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_NO_AGENT = The Agent[#{[agentName]}] where task #{[taskName]} is located has stopped running, there is no more Agent currently available, it will affect the normal operation of the task, please deal with it as soon as possible! Alarm time: #{[alarmDate]}
SYSTEM_FLOW_EGINGE_RECOVER = Task #{[taskName]} in Agent [#{[agentName]}] is back up and running, alarm time: #{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_CLOUD = The Agent[#{[agentName]}] where task #{[taskName]} is located has stopped running and will affect the normal operation of the task, please deal with it as soon as possible! Alarm time: #{[alarmDate]}
GREATER=greater
LESS=less
INSPECT_TASK_ERROR =Your verification task [#{[inspectName]}] has stopped due to an error, time: #{[alarmDate]}, please pay attention.
INSPECT_COUNT_ERROR = Your verification task [#{[inspectName]}] quick count verification results are inconsistent, the current number of differences is: #{[count]}, please pay attention. Alarm time: #{[alarmDate]}
INSPECT_VALUE_ALL_ERROR = Your verification task [#{[inspectName]}] table full field value verification results are inconsistent, the current table data is poor: #{[count]}, please pay attention. Alarm time: #{[alarmDate]}
INSPECT_VALUE_JOIN_ERROR = Your verification task [#{[inspectName]}] associated field value verification results are inconsistent, the current table data is poor: #{[count]}, please pay attention. Alarm time: #{[alarmDate]}
TASK_STATUS_STOP = Task #{[taskName]} has stopped, stop time point: #{[stopTime]}, alarm time: #{[alarmDate]}
