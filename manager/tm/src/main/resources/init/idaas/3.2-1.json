[{"insert": "Permission", "documents": [{"description": "高级功能", "name": "v2_advanced_features", "need_permission": true, "order": 1, "parentId": "", "resources": [{"type": "page", "code": "v2_advanced_features", "path": ""}], "status": "enable", "type": "read", "version": "v2"}]}, {"insert": "RoleMapping", "documents": [{"principalId": "v2_advanced_features", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"principalId": "v2_advanced_features", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}]}, {"update": "Permission", "updates": [{"q": {"name": "v2_shared_cache"}, "u": {"$set": {"parentId": "v2_advanced_features"}}, "upsert": false, "multi": false}, {"q": {"name": "v2_log_collector"}, "u": {"$set": {"parentId": "v2_advanced_features"}}, "upsert": false, "multi": false}, {"q": {"name": "v2_function_management"}, "u": {"$set": {"parentId": "v2_advanced_features"}}, "upsert": false, "multi": false}, {"q": {"name": "v2_custom_node"}, "u": {"$set": {"parentId": "v2_advanced_features"}}, "upsert": false, "multi": false}]}]