[{"insert": "Permission", "documents": [{"description": "Data Console", "name": "v2_data-console", "need_permission": true, "order": 1, "parentId": "", "resources": [{"type": "page", "code": "v2_data-console", "path": "/data-console"}], "status": "enable", "type": "read", "version": "v2"}]}, {"insert": "RoleMapping", "documents": [{"principalId": "v2_data-console", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"principalId": "v2_data-console", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}]}]