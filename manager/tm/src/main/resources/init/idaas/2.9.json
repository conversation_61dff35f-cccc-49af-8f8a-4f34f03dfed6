[{"update": "Settings", "updates": [{"q": {"key": "retry_interval_second"}, "u": {"$set": {"category": "Job", "default_value": "60", "user_visible": true, "key_label": "retry_interval_second"}, "$setOnInsert": {"value": "60"}}, "upsert": true}, {"q": {"key": "max_retry_time_minute"}, "u": {"$set": {"category": "Job", "default_value": "60", "user_visible": true, "key_label": "max_retry_time_minute"}, "$setOnInsert": {"value": "60"}}, "upsert": true}]}, {"dropIndexes": "MetadataDefinition", "index": "value_1"}, {"createIndexes": "MetadataDefinition", "indexes": [{"name": "value_1", "key": {"value": 1}, "background": true}]}, {"createIndexes": "MetadataInstances", "indexes": [{"name": "index_task_id", "key": {"taskId": 1}, "background": true}]}, {"dropIndexes": "AgentMeasurementV2", "index": "tags.taskId_1_tags.taskRecordId_1_tags.type_1_grnty_1_date_-1"}, {"createIndexes": "AgentMeasurementV2", "indexes": [{"name": "tags.taskId_1_tags.taskRecordId_1_tags.engineId_1_tags.type_1_grnty_1_date_-1", "key": {"tags.taskId": 1, "tags.taskRecordId": 1, "tags.engineId": 1, "tags.type": 1, "grnty": 1, "date": -1}, "background": true, "sparse": true}, {"name": "tags.taskId_1_tags.taskRecordId_1_tags.type_1_grnty_1_date_-1", "key": {"tags.taskId": 1, "tags.taskRecordId": 1, "tags.type": 1, "grnty": 1, "date": -1}, "background": true, "sparse": true}]}]