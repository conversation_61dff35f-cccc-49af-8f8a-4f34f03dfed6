[
  {
    "update": "Task",
    "updates": [
      {
        "q": {
          "dag.nodes": {
            "$elemMatch": {
              "migrateTableSelectType": "all"
            }
          }
        },
        "u": {
          "$set": {
            "dag.nodes.$[element].migrateTableSelectType": "expression",
            "dag.nodes.$[element].tableExpression": ".*"
          }
        },
        "arrayFilters": [ { "element.migrateTableSelectType": "all" , "element.tableNames": {$exists:true} } ],
        "upsert": false,
        "multi": true
      }
    ]
  }
]
