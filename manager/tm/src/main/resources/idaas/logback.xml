<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="3 seconds">
	<property name="logName" value="tm" />
	<property name="logPath" value="${TAPDATA_WORK_DIR:-.}/logs/manager"/>
	<contextName>${logName}</contextName>

	<define name="HOSTNAME" class="com.tapdata.tm.config.CanonicalHostNamePropertyDefiner"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>
				%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<appender name="APP_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logPath}/${logName}-${HOSTNAME}.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logPath}/${logName}-${HOSTNAME}-%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>1024MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>7</maxHistory>
		</rollingPolicy>
		<layout class="com.tapdata.tm.utils.CustomPatternLayout">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%contextName] [%thread] %-5level %logger{36} - %msg%n</pattern>
		</layout>
	</appender>

	<!-- 异步输出 -->
	<appender name="ASYNC-INFO" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="APP_LOG_FILE"/>
	</appender>

	<jmxConfigurator />
	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="APP_LOG_FILE" />
	</root>

	<logger name="com.tapdata" level="INFO" />
	<logger name="com.tapdata.tm.base.filter" level="INFO" />
	<logger name="org.springframework.data.mongodb" level="INFO"/>
    <!--  过滤 mongo 查询日志信息：Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type  -->
	<logger name="org.springframework.data.mongodb.core.convert.QueryMapper" level="WARN"/>
	<logger name="org.mongodb.driver" level="INFO"/>
	<logger name="net.javacrumbs.shedlock.core" level="INFO"/>
	<!--<logger name="com.tapdata.manager.iaas.k8s.interceptor" level="DEBUG"/>-->

</configuration>
