check.host.port=connected succeed!
check.host.port.fail=Unable connect to host and port
check.host.port.reason=Connection refused
check.host.port.solution=Ensure telnet host and port successfully and server is available.
pdk.connection=login succeed!
pdk.auth.fail=Username or password error
pdk.auth.reason=1. Wrong filling;\n2. The password contains special characters.
pdk.auth.solution=1. Try to fill in the password again, and conduct a connection test;\n2. Try to change the password without special characters.
pdk.connection.fail=The client connection was closed by the server.
pdk.connection.reason=1. The server manually closed the connection;\n2. The server has too many connections, automatically closed or rejected subsequent connections.
pdk.connection.solution=1. Ensure database server is available;\n2. Check if the amount of sever connection has exceeded limitation.
pdk.version.fail=Query Version failed.
pdk.version.reason=1. The server is down;\n2. There has no privilege on query version
pdk.version.solution=Ensure current user has enough privilege to query version.
read.privilege.fail=When reading data, the corresponding permission is missing.
read.privilege.reason=1. The user in the data connection used does not have the corresponding read permission;\n2. For some databases, more permissions are required for incremental reading. Please refer to the instructions on the right side of creating a data source, confirm that the permissions are set correctly.
read.privilege.solution=1. Please make sure current user has enough privilege to execute query;\n2. Check whether the username in the data connection used by the target node lacks read permission.
write.privilege.fail=When writing data, the corresponding permission is missing.
write.privilege.reason=The user in the data connection used does not have the corresponding to write permission.
write.privilege.solution=Check whether the username in the data connection used by the target node lacks write permission.
stream.read.fail=Test log plugin failed, maybe cdc events cannot work.
stream.read.reason=Cdc configuration is incorrect.
stream.read.solution=You need to do more configuration for the CDC of the data source.
check.cdc.privilege.fail=Check cdc privileges failed.
check.cdc.privilege.reason=1. The server manually closed the connection;\n2. CDC may configure incorrectly.
check.cdc.privilege.solution=Please check cdc configuration.
create.table.privilege.fail=Check create table privileges failed.
create.table.privilege.reason=When creating table, the corresponding permission is missing.
create.table.privilege.solution=Check whether the username in the data connection used by the target node lacks create table permission.
time.consistent.fail=Failed to obtain current database time.
time.consistent.reason=1. The server is down;\n2. There is no privilege when obtain database time.
time.consistent.solution=1. Ensure database server is available;\n2. Ensure current user has enough privilege to obtain database time.

pdk.unknown.fail=Test connection failed.
pdk.unknown.reason=The server occurs an error.
pdk.unknown.solution=1. Ensure database server is available;\n2. Report the issue to Tapdata.