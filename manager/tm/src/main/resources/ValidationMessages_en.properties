SystemError=System error: {0}
IllegalState=Illegal State: {0}
IllegalArgument=Illegal Argument: {0}
javax.validation.constraints.ValueOfEnum.message=Must be any of enum {enums}
javax.validation.constraints.CustomerTypeSubset.message=Must be any {anyOf}
javax.validation.constraints.In.message=Must be in {values}
NotLogin=User is not login.

CreateAgentFailed.AccessFailed=Cannot access TM server.
SubscribeFailed.OrderLimit=A maximum of {0} instances can be ordered in the same resource pool
QueryUserInfoFromAuthingFailed=Failed to call Authing query user information
Resources.NotEnough=Resources not enough



#inspect
inspect.name.exist=name already exists
Inspect.agentTag.null=inspect agent must not be null
Inspect.task.null=inspect task must not be null

#function
function.name.exist=name already exists


#MetadataDefinition
MetadataDefinition.Value.Exist=MetadataDefinition already exists


#reset password
Email.Not.Exist=email not exist
Email.Send.fail=send email failed
Email.verify.code.Send.fail=Send email failed, message: {0}


#modules
Modules.Name.Existed=api name already exists
Modules.BasePathAndVersion.Existed=api basePath already exists
Modules.Connection.Null= this Connection is null
Datasource.LinkModules= has referenced api


User.Not.Exist= user not exist
User.email.Found=can not find this email
ValidateCode.Not.Incorrect=validateCode is incorrect
ValidateCode.Is.expired=validateCode Expires


