SystemError=System error: {0}
IllegalState=Illegal State: {0}
IllegalArgument=Illegal Argument: {0}
javax.validation.constraints.ValueOfEnum.message=Must be any of enum {enums}
javax.validation.constraints.CustomerTypeSubset.message=Must be any {anyOf}
javax.validation.constraints.In.message=Must be in {values}
NotLogin=User is not login.
NotAuthorized=user has no authorized

CreateAgentFailed.AccessFailed=Cannot access TM server.
SubscribeFailed.OrderLimit=A maximum of {0} instances can be ordered in the same resource pool
QueryUserInfoFromAuthingFailed=Failed to call Authing query user information
Resources.NotEnough=Resources not enough

Inspect.Name.Exist=name already exists
Inspect.ProcessId.NotFound=inspect processid notfound
Inspect.agentTag.null=inspect agent must not be null
Inspect.task.null=inspect task must not be null
Inspect.Recovery.NotFieldMethod = Not field inspection task
Inspect.Recovery.StatusNotDone = Inspection task is not in a 'done' status
Inspect.Recovery.ResultNotFound = Inspection task has no discrepancy results
Inspect.Recovery.IsNotWithTask = Inspection is not created based on a synchronization task
Inspect.Recovery.TaskNotFound = Synchronization task does not exist
Inspect.Recovery.TaskTypeIsNotCDC = Synchronization task does not include incremental phase
Inspect.Recovery.TaskIsNotRunning = Synchronization task is not in a 'running' status
Inspect.Recovery.TaskSyncStatusIsNotCDC = Synchronization task is not in the incremental phase

Inspect.ExportEventSql.TargetConnectionNotSupport = The target data source does not currently support exporting repair SQL
Inspect.ExportEventSql.TargetConnectionNotFound =Target connection does not exist
Inspect.ExportEventSql.fail = Export repair SQL failed
Inspect.ExportEventSql.Exporting=Repair file is being exported
Inspect.ExportEventSql.Exported=Repair file exported

AccessCode.Is.Null=AccessCode is null
AccessCode.No.User=can not find user by this accessCode


Function.Name.Exist=name already exists
Upload.File.NotExist=name already exists


# DataFlow
DataFlow.Not.Found=DataFlow does not exist
Not.Supported=Not supported
Name.Already.Exists=Name already exists
Transition.Not.Supported=This event is not supported in the state
Transition.Not.Found=Transition not found

Agent.Not.Found=No agent available
UserId.Not.Found=UserId not found
User.Not.Found=User not found
User.Already.Exists=User already exists
Role.Already.Exists=Role already exists
Incorrect.Password=The password is incorrect
Incorrect.Vague=Email or password is incorrect, please check and re-enter

Resource.Not.Found=Resource not found

UserGroup.Exists.User=Unable to delete, there are users in the current user group
Role.Unable.Delete=The role cannot be deleted because a user is bound to the role

Task.DDL.Conflict.Sync=The task contains JS nodes, custom nodes, or incremental custom SQL for node settings. DDL is not supported temporarily. Please close it manually
Task.DDL.Conflict.Migrate=The task contains JS nodes, and DDL is not supported temporarily. Please close it manually
Task.Save.Error=Task saving failed. Please check the log information for specific reasons

Too.Many.Login.Failures=Login failed, please try again later

#reset password
Email.Not.Exist=email not exist
Email.Send.fail=send email failed
Email.verify.code.Send.fail=Send email failed, message: {0}
Email.Format.wrong = Email format error
Account.disabled=User is frozen

#modules
Modules.Name.Existed=api name already exists
Modules.BasePathAndVersion.Existed=api basePath already exists
Modules.Connection.Null= this Connection is null
Modules.Permission.Scope.Null = permission scope cannot be null
Modules.Tags.Null = tags cannot be null


User.Not.Exist= user not exist
User.email.Found=can not find this email
ValidateCode.Not.Incorrect=validateCode is incorrect
ValidateCode.Is.expired=validateCode Expires
AD.Login.Fail=Login by LDAP failed, please contact the administrator
AD.Search.Fail=Search LDAP user failed, please contact the administrator
AD.Account.Not.Exists=LDAP account does not exist in current group, please contact the administrator
AD.Login.WrongPassword=Login by LDAP failed, invalid password or credentials, please check your account is available and password is correct
AD.Login.InvalidCert=Certificate authentication failed, please ensure certificate is valid
AD.Login.Retryable=There is a network problem, please try again or contact the administrator

Task.CanNotSupportInspect=data source not support inspect
InvalidPaidPlan=Invalid paid plan, please check and subscribe paid plan

# external storage
External.Storage.Name.Blank=Name cannot be null
External.Storage.Name.Exists=Name {0} already exists
External.Storage.Type.Blank=Storage type cannot be null
External.Storage.Type.Invalid=Illegal storage type: {0}
External.Storage.MongoDB.Uri.Blank=MongoDB uri cannot be null
External.Storage.MongoDB.Database.Blank=MongoDB database cannot be null
External.Storage.MongoDB.Table.Blank=MongoDB collection name cannot be null
External.Storage.RocksDB.Path.Blank=RocksDb path cannot be null
External.Storage.Cannot.Delete=Cannot delete, the configuration is in use\nTask number: {0}\nTask name: {1}
External.Storage.ID.NULL=External storage id cannot be null
External.Storage.MongoDB.Old.Pwd.NULL=Failed to replace the masked password, the password extracted from the old MongoDB connection information is empty: {0}

v2_dashboard=Console
v2_data-console=Data Console
v2_datasource_menu=Connections
v2_data_pipeline=Data Pipelines
v2_data_replication=Replications
v2_data_flow=Transforms
v2_data_check=Validations
v2_advanced_features=Advanced Features
v2_log_collector=CDC Log Cache
v2_function_management=Function List
v2_custom_node=User Defined Processors
v2_shared_cache=Live Cache
v2_data_discovery=Data Discovery
v2_data_object=Data Object
v2_data_catalogue=Data Catalog
v2_data-server=Data Services
v2_api-application=Application List
v2_data-server-list=API List
v2_api-client=API Clients
v2_api-servers=API Servers
v2_data_server_audit=API Audit
v2_api_monitor=API Status
v2_system-management=System
v2_external-storage_menu=External Storage Settings
v2_cluster-management_menu=Cluster
v2_user_management_menu=Users
v2_role_management=Roles

JsFunction.ImportFormatError=Function import format is incorrect
JsFunction.ImportFormatError.function.name.empty=Function name cannot be empty
JsFunction.ImportFormatError.function.body.empty=Function body cannot be empty

share_cdc_task_stop_warning=The shared mining task used by this task has been stopped, please restore it as soon as possible
task_status_error=error
task_status_stop=stop

#Type Message
ARRAY=Array
BINARY=ByteArray
BOOLEAN=Boolean
DATE=Date
DATETIME=DateTime
MAP=Map
NUMBER=Number
STRING=String
TIME=Time
YEAR=Year
UNKNOW=Unknow

#Log Message
AllNotices=AllNotices
DeleteNotifications=DeleteNotifications
NotificationSettings=NotificationSettings
ReadNotifications=ReadNotifications
EnterpriseInformation=EnterpriseInformation

DAG.JonNode=The Join node needs to be configured with two front-end nodes

#TapOssNonSupportFunctionException
TapOssNonSupportFunctionException=Oss version is not support this function

#AgentGroup
group.repeat=Name already exists
group.agent.not.fund=The current agent does not exist: {0}
group.agent.repeatedly=The engine cannot be added to the same tag repeatedly
group.agent.add.failed=Engine failed to add tag, please try again
group.agent.remove.failed=Engine failed to remove label, please try again
group.response.body.failed=Request parameter cannot be empty
group.id.empty=The tag ID cannot be empty
group.agent.id.empty=The agent ID cannot be empty
group.new.name.empty=The new name cannot be empty
group.new.name.too.long=The new name length to long, must be less than {0}
group.not.fund=Agent tag does not exist: {0}
group.agent.not.available=There are currently no available engine associations for the current tag: {0}
group.agent.not.invalid=Invalid tag ID
lack.group.agent=Please supplement the Agent label

#task import
task.import.connection.check.ConnectorNotRegister=Task import failed. Please register data source {0} and dependent version {1} before importing. If it is already registered, please try again
task.import.connection.check.ConnectorVersionTooHeight=Task import failed. The version of connector {0} used in the import task is too high. The current environment is using version {1}, and the version in the import task is {2}. Please upgrade connector {3} and import task again

relMig.parse.failed=relmig file parsing failed, message: {0}
relMig.parse.unSupport=The current relmig file version {0} is not currently supported, and only supports: {1}

#web hook
webhook.create.hook.defaultName=<Default Service Hook Name>
webhook.url.too.long=The maximum length allowed for URL to be filled in is: {0}, actual length filled in: {1}
webhook.info.mark.too.long=The maximum length allowed for note text to be filled in is: {0}, actual length filled in: {1}
webhook.info.custom.http.headers.too.long=The maximum length allowed for custom request header text is: {0}, actual length filled in: {1}
webhook.info.custom.template.too.long=The maximum length allowed for filling in custom template text is: {0}, actual length filled in: {1}
webhook.info.custom.template.invalid=The custom request body template is invalid
webhook.info.custom.http.headers.invalid=The custom HTTP request header information is invalid, {0}
webhook.ping.failed=Ping failed {0}, please check your WebHook URL
webhook.url.host.invalid=Illegal IP and domain name configuration, unable to use the current system's IP port：{0}
webhook.ping.succeed=The testing PING event has been sent to the server. Please check the sending record to confirm the result
webhook.url.invalid=Parameter verification failed, parameter attribute service_URL format is incorrect. Actual value: {0} Expected type: Legal URI
webhook.info.existsById=WebHook configuration information does not exist, WebHook ID: {0}
webhook.history.reSend=Before re-send an history webhook event message, this history event ID must not be empty
webhook.reOpen.failed=WebHook re-open failed, please retry again
webhook.history.hook.id.error=WebHook ID must not be empty
webhook.history.page.from.error=The paging parameter [pageFrom] is invalid, the starting value should be greater than or equal to 0, actual value is: {0}
webhook.history.page.size.error=The paging parameter [pageSize] is invalid, the page size value should be greater than to 0, actual value is: {0}

#auth
insufficient.permissions=Insufficient permissions: {0}, current operation requires permission: {1}
inspect.result.not.exists=The verification record for the current verification task no longer exists: {0}
tag.operate.not.allowed=Current user has no permissions to operate the tag

source.setting.check.cdc=[cdc check] The configuration of the data source:{0} does not support incremental functionality, and incremental tasks cannot be configured. 

export.inspectDetails.failed = Export inspectDetails failed :{0}
read.version.file.failed=Invalid system version

openapi.generator.version.exists=The version already exists
openapi.generator.package.name.empty=Package name cannot be empty
openapi.generator.package.name.invalid.format=Package name format is invalid. Must follow Java package naming conventions: lowercase letters, digits, underscores, separated by dots, each segment must start with a letter
openapi.generator.package.name.contains.reserved.keyword=Package name contains Java reserved keyword: {0}
openapi.generator.reentry.blocked=Another version is currently generating for sdk name: {0}. Please wait for the current generation to complete before starting a new one
openapi.generator.unknown.error=Failed to async code generation: {0}
openapi.generator.module.empty=Please select the API and generate SDK again

module.save.check.not-empty=The field list in the output result cannot be empty
module.save.check.repat=The field alias in the output result cannot be set repeatedly: {0}