<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.tapdata</groupId>
		<artifactId>tm-parent</artifactId>
		<version>0.0.1</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>tm</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>tm-lib</name>
	<description>Tapdata Management</description>
	<packaging>jar</packaging>

	<properties>
		<resource.dir>src/main/resources</resource.dir>
		<maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
		<okhttp3.version>4.8.0</okhttp3.version>
	</properties>

	<profiles>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
				<property>
					<name>!env.GIT_COMMIT_VERSION</name>
				</property>
			</activation>
			<properties>
				<profile.active>dev</profile.active>
				<profile.dir>${resource.dir}/dev</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<profile.active>prod</profile.active>
				<profile.dir>${resource.dir}/prod</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<profile.active>test</profile.active>
				<profile.dir>${resource.dir}/test</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>uat</id>
			<properties>
				<profile.active>uat</profile.active>
				<profile.dir>${resource.dir}/uat</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>sit</id>
			<properties>
				<profile.active>sit</profile.active>
				<profile.dir>${resource.dir}/sit</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>idaas</id>
			<properties>
				<profile.active>idaas</profile.active>
				<profile.dir>${resource.dir}/idaas</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>idaas</env.PRODUCT>
			</properties>
		</profile>
	</profiles>

	<dependencies>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>tapdata-proxy</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>mongodb-storage-module</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>common</artifactId>
			<version>2.2.2</version>
		</dependency>

		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>tm-common</artifactId>
			<version>0.0.3-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>tapdata-common</artifactId>
			<version>0.2.22-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.17</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.17</version>
		</dependency>

		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.6</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-webmvc-core</artifactId>
			<version>1.4.6</version>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>1.4.6</version>
		</dependency>

		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-spring</artifactId>
			<version>4.15.1</version>
		</dependency>
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-provider-mongo</artifactId>
			<version>4.15.1</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>2.0.9</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
				<exclusion>
					<artifactId>objenesis</artifactId>
					<groupId>org.objenesis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>2.0.9</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
			<groupId>com.networknt</groupId>
			<artifactId>json-schema-validator</artifactId>
			<version>1.0.58</version>
		</dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.66</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.5.2</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.10.3</version>
		</dependency>
		<!--阿里云短信sdk-->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.1.0</version>
			<exclusions>
				<exclusion>
					<artifactId>activation</artifactId>
					<groupId>javax.activation</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>2.1.1</version>
		</dependency>

		<!-- Spring security start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-crypto</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-rsa</artifactId>
			<version>1.0.10.RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>bcprov-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<!-- OAuth2 authorization server -->
		<dependency>
			<groupId>org.springframework.security.experimental</groupId>
			<artifactId>spring-security-oauth2-authorization-server</artifactId>
			<version>0.1.2</version>
		</dependency>
		<!-- Spring security end -->


		<dependency>
			<scope>compile</scope>
			<groupId>com.harium.graph</groupId>
			<artifactId>graph</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>net.rakugakibox.spring.boot</groupId>
			<artifactId>orika-spring-boot-starter</artifactId>
			<version>1.9.0</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.4</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.kubernetes</groupId>
			<artifactId>client-java</artifactId>
			<version>14.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>bcpkix-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections4</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.quartz-scheduler/quartz -->
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.3.0</version>
		</dependency>

		<dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<version>5.8.5</version>
		</dependency>

	</dependencies>

	<build>

		<finalName>${project.name}-${project.version}</finalName>
		<resources>
			<resource>
				<directory>${project.basedir}/${resource.dir}</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>dev/*</exclude>
					<exclude>prod/*</exclude>
					<exclude>test/*</exclude>
					<exclude>sit/*</exclude>
					<exclude>idaas/*</exclude>
					<exclude>*.yaml</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>${project.basedir}/${profile.dir}</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>Dockerfile</exclude>
					<exclude>*.yaml</exclude>
					<exclude>*.js</exclude>
				</excludes>
				<!--<targetPath>../</targetPath>-->
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>8</source>
					<target>8</target>
				</configuration>
			</plugin>

		</plugins>

		<!--<extensions>
			<extension>
				<groupId>kr.motd.maven</groupId>
				<artifactId>os-maven-plugin</artifactId>
				<version>1.4.0.Final</version>
			</extension>
		</extensions>-->
	</build>
	<distributionManagement>
		<repository>
			<!--必须与 settings.xml 的 id 一致-->
			<id>tapdata-tapdata-maven</id>
			<name>maven</name>
			<url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
		</repository>
	</distributionManagement>
</project>
