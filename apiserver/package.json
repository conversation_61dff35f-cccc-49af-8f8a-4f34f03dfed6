{"name": "@tapdata/moa", "version": "1.0.1", "description": "Open API", "keywords": ["API Server"], "bin": {"moa": "bin/start.js"}, "engines": {"node": ">=8.9"}, "scripts": {"build:apidocs": "lb-apidocs", "build": "lb-tsc es2017 --outDir dist", "build:watch": "lb-tsc --watch", "clean:dist": "lb-clean dist", "clean:api": "lb-clean src/controllers/*.ts && lb-clean src/repositories/*.ts && lb-clean src/models/*.ts && lb-clean src/datasources/*.ts", "clean": "lb-clean dist && npm run clean:api && lb-clean logs && lb-clean *.pid && lb-clean cache", "lint:fix": "npm run tslint:fix && npm run prettier:fix", "prettier:fix": "npm run prettier:cli -- --write", "tslint": "lb-tslint", "tslint:fix": "npm run tslint -- --fix", "pretest": "npm run clean && npm run build", "test": "echo \"no test specified.\"", "test:dev": "lb-mocha --allow-console-logs dist/test/**/*.js", "prestart": "npm run build", "start": "node .", "dev": "node .", "prepublishOnly": "npm run test", "run": "node app.js", "postinstall": "node post-install.js"}, "homepage": "https://github.com/tapd8/apig/tree/moa", "repository": {"type": "git", "url": "https://github.com/tapd8/apig/tree/moa"}, "author": "", "license": "", "dependencies": {"@loopback/authentication": "^1.0.6", "@loopback/boot": "^1.0.3", "@loopback/context": "^1.0.1", "@loopback/core": "^1.0.1", "@loopback/http-server": "latest", "@loopback/openapi-v3": "^1.1.0", "@loopback/repository": "^1.0.3", "@loopback/rest": "^1.2.0", "@loopback/service-proxy": "^1.0.1", "@types/mime": "^2.0.3", "@types/mongodb": "^3.1.22", "@types/socket.io": "^2.1.4", "auditjs": "^4.0.46", "axios": "^1.7.7", "change-case": "^3.0.2", "conf": "^5.0.0", "cross-spawn": "^6.0.5", "download-file": "^0.1.5", "eureka-nodejs-client": "^1.0.7", "exceljs": "^3.5.0", "express": "file:./local-deps/express", "express-graphql": "0.12.0", "express-ws": "^4.0.0", "graphql": "^16.11.0", "hashcode": "^1.0.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.11", "log4js": "^6.9.1", "loopback-connector-mongodb": "file:./local-deps/loopback-connector-mongodb", "loopback-connector-mssql": "file:./local-deps/loopback-connector-mssql", "loopback-connector-mysql": "file:./local-deps/loopback-connector-mysql", "loopback-connector-postgresql": "file:./local-deps/loopback-connector-postgresql", "loopback-datasource-juggler": "file:./local-deps/loopback-datasource-juggler", "make-dir": "^1.3.0", "mem-fs": "^2.3.0", "mem-fs-editor": "^8.1.2", "moment": "^2.24.0", "mongodb": "3.6.11", "mongodb-core": "^3.2.7", "mongodb-schema": "^12.6.2", "msgpack5": "file:./local-deps/msgpack5/", "multer": "^2.0.0", "openapi-to-graphql": "^2.6.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prompts": "^2.1.0", "scan-fs": "0.0.5", "socket.io": "^4.8.1", "through2": "^3.0.0", "typescript": "^4.8.3", "ws": "latest"}, "devDependencies": {"@loopback/build": "^1.0.1", "@loopback/testlab": "^1.0.1", "@types/node": "^10.11.2", "@types/node-fetch": "^2.6.3", "@types/passport": "^0.4.7", "@types/passport-jwt": "^4.0.1", "query-string": "^6.2.0", "request-promise": "^4.2.5"}, "overrides": {"async": "^3.2.6", "axios": "^1.7.7", "braces": "^3.0.3", "busboy": "^1.6.0", "dicer": "^0.3.1", "ejs": "^3.1.10", "passport": "^0.7.0", "semver": "^7.7.2", "tough-cookie": "^4.1.4", "cookie": "^0.7.2", "json-ptr": "^3.1.1", "jsonpath-plus": "^10.3.0", "brace-expansion": "^1.1.12", "graphql": "^16.11.0", "express-graphql": {"graphql": "^16.11.0"}, "express-ws": {"express": "^5.0.0"}}}